
#!/usr/bin/env python3
"""
新闻预测交易策略

基于新闻数据和技术指标的交易策略
"""

import pickle
import numpy as np
import sentencepiece as spm
from typing import List, Optional
from decimal import Decimal
from datetime import datetime, timedelta

# Nautilus Trader imports
from nautilus_trader.common.component import MessageBus
from nautilus_trader.config import ActorConfig, StrategyConfig
from nautilus_trader.core.uuid import UUID4
from nautilus_trader.indicators.atr import AverageTrueRange
from nautilus_trader.indicators.rsi import RelativeStrengthIndex
from nautilus_trader.indicators.mfi import MoneyFlowIndex
from nautilus_trader.model.data import Bar, BarType
from nautilus_trader.model.enums import OrderSide
from nautilus_trader.model.identifiers import InstrumentId, StrategyId
from nautilus_trader.model.instruments import Instrument
from nautilus_trader.trading.strategy import Strategy
from nautilus_trader.core.uuid import UUID4
from nautilus_trader.data.messages import RequestBars
# 自定义数据类型
from news_data_type import NewsData
from crypto_text_processor import CryptoNewsTextProcessor

class OptimalNewsPredictor:
    def __init__(
        self,
        tokenizer_path: str,
        tfidf_path: str,
        model_path: str
    ):
        self.tokenizer_path = tokenizer_path
        self.tfidf_path = tfidf_path
        self.model_path = model_path

        # 新闻处理组件
        self.text_processor = CryptoNewsTextProcessor()

        # 初始化模型组件为None
        self.sp_model = None
        self.tfidf_vectorizer = None
        self.rf_model = None

        # 加载模型组件
        self._load_models()

    def _load_models(self):
        """加载所有模型组件"""
        print("🔄 开始加载模型组件...")

        # 1. 加载SentencePiece模型
        if self._load_sentencepiece_model():
            print("✅ SentencePiece模型加载成功")
        else:
            print("❌ SentencePiece模型加载失败")

        # 2. 加载TF-IDF vectorizer
        if self._load_tfidf_vectorizer():
            print("✅ TF-IDF vectorizer加载成功")
        else:
            print("❌ TF-IDF vectorizer加载失败")

        # 3. 加载RandomForest模型
        if self._load_rf_model():
            print("✅ RandomForest模型加载成功")
        else:
            print("❌ RandomForest模型加载失败")

        # 检查所有组件是否加载成功
        if self.sp_model and self.tfidf_vectorizer and self.rf_model:
            print("🎉 所有模型组件加载成功，可以使用完整预测流程")
        else:
            print("⚠️ 部分模型组件加载失败，将使用简化预测流程")

    def _load_sentencepiece_model(self) -> bool:
        """加载SentencePiece模型"""
        try:
            import os
            if not os.path.exists(self.tokenizer_path):
                print(f"SentencePiece模型文件不存在: {self.tokenizer_path}")
                return False

            self.sp_model = spm.SentencePieceProcessor()
            self.sp_model.load(self.tokenizer_path)
            return True
        except Exception as e:
            print(f"SentencePiece模型加载失败: {e}")
            self.sp_model = None
            return False

    def _load_tfidf_vectorizer(self) -> bool:
        """加载TF-IDF vectorizer"""
        try:
            import os
            if not os.path.exists(self.tfidf_path):
                print(f"TF-IDF文件不存在: {self.tfidf_path}")
                return False

            with open(self.tfidf_path, 'rb') as f:
                self.tfidf_vectorizer = pickle.load(f)
            return True
        except Exception as e:
            print(f"TF-IDF vectorizer加载失败: {e}")
            self.tfidf_vectorizer = None
            return False

    def _load_rf_model(self) -> bool:
        """加载RandomForest模型"""
        try:
            import os
            if not os.path.exists(self.model_path):
                print(f"RandomForest模型文件不存在: {self.model_path}")
                return False

            with open(self.model_path, 'rb') as f:
                self.rf_model = pickle.load(f)
            return True
        except Exception as e:
            print(f"RandomForest模型加载失败: {e}")
            self.rf_model = None
            return False

    def tokenize_text(self, text: str) -> str:
        """文本分词"""
        if self.sp_model:
            return ' '.join(self.sp_model.encode_as_pieces(text))
        else:
            # 简单分词fallback
            return text.lower()

    def predict(
        self,
        news_signal: NewsData,
        technical_indicators: List[float]
    ) -> tuple:
        """
        预测新闻影响

        遵循原来的处理预测逻辑：
        1. 文本预处理 (CryptoNewsTextProcessor)
        2. SentencePiece分词
        3. TF-IDF特征提取
        4. 合并技术指标
        5. RandomForest预测
        """
        print(f"\n🔍 ===== 新闻预测分析开始 =====")
        print(f"📰 原始新闻标题: {news_signal.title}")
        print(f"📰 原始新闻内容: {news_signal.content[:200]}...")

        # 1. 处理新闻文本 - 使用CryptoNewsTextProcessor
        news_title = self.text_processor.process(news_signal.title)
        news_content = self.text_processor.process(news_signal.content)
        combined_text = news_title + ' ' + news_content
        print(f"� 处理后的新闻文本: {combined_text[:150]}...")
        print(f"📏 文本长度: {len(combined_text)} 字符")
        try:
            # 1. tokenizer分词
            tokenized_text = self.tokenize_text(combined_text)
            print(f"🔤 分词结果: {tokenized_text[:100]}...")

            # 2. TF-IDF特征提取
            X_text = self.tfidf_vectorizer.transform([tokenized_text])
            print(f"📊 TF-IDF特征维度: {X_text.shape}")
            print(f"📊 TF-IDF非零特征数: {X_text.nnz}")

            # 3. 技术指标分析
            tech_features = np.array(technical_indicators).reshape(1, -1)
            print(f"📈 技术指标数量: {len(technical_indicators)}")
            print(f"📈 技术指标值: {technical_indicators}")

            # 合并特征
            X = np.hstack((X_text.toarray(), tech_features))
            print(f"📊 最终特征维度: {X.shape}")
            print(f"📊 文本特征: {X_text.shape[1]}, 技术指标: {tech_features.shape[1]}")

            # 4. RandomForest预测
            predict_label = self.rf_model.predict(X)
            predict_label_prob = self.rf_model.predict_proba(X)

            pred_class = int(predict_label[0])
            all_probs = predict_label_prob[0]
            confidence = float(all_probs[pred_class])

            print(f"🔮 预测结果详情:")
            print(f"   - 预测类别: {pred_class}")
            print(f"   - 类别置信度: {confidence:.3f}")
            print(f"   - 所有类别概率: {[f'{p:.3f}' for p in all_probs]}")
            print(f"   - 类别含义: {self._get_class_meaning(pred_class)}")
            print(f"🔍 ===== 新闻预测分析结束 =====\n")

            return pred_class, confidence
        except Exception as e:
            raise RuntimeError(f"预测过程中发生错误: {e}")

    def _get_class_meaning(self, pred_class: int) -> str:
        """获取预测类别的含义"""
        class_meanings = {
            0: "强烈看跌 (Strong Bearish)",
            1: "看跌 (Bearish)",
            2: "中性 (Neutral)",
            3: "看涨 (Bullish)",
            4: "强烈看涨 (Strong Bullish)"
        }
        return class_meanings.get(pred_class, f"未知类别 ({pred_class})")

# 交易策略配置
class NewsPnlTradingStrategyConfig(StrategyConfig, frozen=True, kw_only=True):
    # 模型路径
    tokenizer_path: str
    tfidf_path: str
    model_path: str

    # 交易工具
    instrument_str: str
    btc_instrument_str: str
    bar_str: str
    btc_bar_str: str

    # 风险管理参数
    stop_loss_pct: float = 0.02
    low_trail_stop_loss_pct: float = 0.5
    trail_stop_loss_pct: float = 0.3
    higher_trail_stop_loss_pct: float = 0.2
    low_trail_profit_threshold: float = 0.01
    first_trail_profit_threshold: float = 0.02
    second_trail_profit_threshold: float = 0.03

    # 交易金额
    max_trade_usd: float = 1000.0
    min_trade_usd: float = 100.0

class NewsPnlTradingStrategy(Strategy):
    def __init__(
        self,
        config: NewsPnlTradingStrategyConfig,
        start_news: Optional[NewsData] = None
    ) -> None:
        # 直接传入config，但确保strategy_id是正确的类型
        super().__init__(config)
        self.predictor = OptimalNewsPredictor(
            tokenizer_path=config.tokenizer_path,
            tfidf_path=config.tfidf_path,
            model_path=config.model_path
        )
        self.instrument_id = InstrumentId.from_str(config.instrument_str)
        self.btc_instrument_id = InstrumentId.from_str(config.btc_instrument_str)
        self.bar_type = BarType.from_str(config.bar_str)
        self.btc_bar_type = BarType.from_str(config.btc_bar_str)
        self.start_news = start_news 

        # 风险管理参数配置
        self.stop_loss_pct = config.stop_loss_pct
        self.low_trail_stop_loss_pct = config.low_trail_stop_loss_pct
        self.trail_stop_loss_pct = config.trail_stop_loss_pct
        self.higher_trail_stop_loss_pct = config.higher_trail_stop_loss_pct
        self.low_trail_profit_threshold = config.low_trail_profit_threshold
        self.first_trail_profit_threshold = config.first_trail_profit_threshold
        self.second_trail_profit_threshold = config.second_trail_profit_threshold

        # 交易金额配置
        self.max_trade_usd = config.max_trade_usd
        self.min_trade_usd = config.min_trade_usd

        self.highest_profit_dict = {}  # 记录每个仓位的最高利润

        # 技术指标 - symbol (MFI, RSI, NATR)
        self.symbol_atr = AverageTrueRange(14)
        self.symbol_rsi = RelativeStrengthIndex(14)
        self.symbol_mfi = MoneyFlowIndex(14)

        # 技术指标 - BTC (MFI, RSI, NATR)
        self.btc_atr = AverageTrueRange(14)
        self.btc_rsi = RelativeStrengthIndex(14)
        self.btc_mfi = MoneyFlowIndex(14)

        self.first_signal = True
        self.take_fee = 0.0004

    @property
    def has_first_signal(self):
        return self.first_signal

    def on_start(self) -> None:
        """策略启动"""
        self.instrument = self.cache.instrument(self.instrument_id)
        if self.instrument is None:
            self.log.error(f"Could not find instrument for {self.instrument_id}")
            self.stop()
            return

        # 订阅symbol和btc的5分钟K线数据
        self.subscribe_bars(self.bar_type)
        self.subscribe_bars(self.btc_bar_type)

        # 注册技术指标 - symbol
        self.register_indicator_for_bars(self.bar_type, self.symbol_atr)
        self.register_indicator_for_bars(self.bar_type, self.symbol_rsi)
        self.register_indicator_for_bars(self.bar_type, self.symbol_mfi)

        # 注册技术指标 - BTC
        self.register_indicator_for_bars(self.btc_bar_type, self.btc_atr)
        self.register_indicator_for_bars(self.btc_bar_type, self.btc_rsi)
        self.register_indicator_for_bars(self.btc_bar_type, self.btc_mfi)

        # 请求历史数据用于技术指标计算
        # 获取当前时间并设置历史数据起始时间（30天前）
        current_time = self.clock.timestamp()
        start_time = current_time - timedelta(days=30)

        self.request_bars(
            bar_type=self.bar_type,
            start=start_time
        )
        self.request_bars(
            bar_type=self.btc_bar_type,
            start=start_time
        )

    def on_news_signal(self, news_signal: NewsData) -> bool:
        """处理新闻信号并执行交易决策"""
        print(f"\n🚨 ===== 交易信号处理开始 =====")

        # 获取技术指标
        tech_features = self._get_technical_indicators()
        if not tech_features:
            print(f"❌ 技术指标获取失败，跳过交易")
            return False

        # 执行新闻预测
        predict_label, predict_label_prob = self.predictor.predict(news_signal, tech_features)
        if predict_label is None:
            print(f"❌ 新闻预测失败，跳过交易")
            return False

        # 计算信号强度和交易金额
        signal_strength = (predict_label - 2.0) / 2.0  # 转换为[-1,1]范围
        trade_usd = self.max_trade_usd * abs(signal_strength)

        print(f"💰 交易决策分析:")
        print(f"   - 预测类别: {predict_label}")
        print(f"   - 信号强度: {signal_strength:.3f}")
        print(f"   - 最大交易金额: ${self.max_trade_usd}")
        print(f"   - 计算交易金额: ${trade_usd:.2f}")
        print(f"   - 最小交易金额: ${self.min_trade_usd}")

        if trade_usd < self.min_trade_usd:
            print(f"❌ 交易金额太小 (${trade_usd:.2f} < ${self.min_trade_usd})，跳过交易")
            return False

        # 执行交易策略
        if predict_label > 2:
            print(f"📈 执行看涨策略: 做多{self.instrument_id}，做空BTC")
            self._execute_long_symbol_short_btc(trade_usd)
        elif predict_label < 2:
            print(f"📉 执行看跌策略: 做空{self.instrument_id}，做多BTC")
            self._execute_short_symbol_long_btc(trade_usd)
        else:
            print(f"😐 中性信号，不执行交易")
            return False

        print(f"🚨 ===== 交易信号处理结束 =====\n")
        return True

    def _execute_long_symbol_short_btc(self, trade_usd: float) -> None:
        """执行symbol多头和BTC空头交易"""
        current_symbol_bar = self.cache.bar(self.bar_type, index=0) 
        if not current_symbol_bar:
            return None
        current_btc_bar = self.cache.bar(self.btc_bar_type, index=0)
        if not current_btc_bar:
            return None

        # 计算symbol和BTC的交易数量
        symbol_trade_qty = trade_usd / current_symbol_bar.close
        btc_trade_qty = trade_usd / current_btc_bar.close

        # 获取BTC工具
        btc_instrument = self.cache.instrument(self.btc_instrument_id)
        if not btc_instrument:
            self.log.error(f"无法找到BTC工具: {self.btc_instrument_id}")
            return None

        symbol_order = self.order_factory.market(
            instrument_id=self.instrument_id,
            order_side=OrderSide.BUY,
            quantity=self.instrument.make_qty(symbol_trade_qty),
        )
        btc_order = self.order_factory.market(
            instrument_id=self.btc_instrument_id,
            order_side=OrderSide.SELL,
            quantity=btc_instrument.make_qty(btc_trade_qty),
        )
        self.submit_order(symbol_order)
        self.submit_order(btc_order)
      

    def _execute_short_symbol_long_btc(self, trade_usd: float) -> None:
        """执行symbol空头和BTC多头交易"""
        current_symbol_bar = self.cache.bar(self.bar_type, index=0)
        if not current_symbol_bar:
            return None
        current_btc_bar = self.cache.bar(self.btc_bar_type, index=0)
        if not current_btc_bar:
            return None

       # 计算symbol和BTC的交易数量
        symbol_trade_qty = trade_usd / current_symbol_bar.close
        btc_trade_qty = trade_usd / current_btc_bar.close

        # 获取BTC工具
        btc_instrument = self.cache.instrument(self.btc_instrument_id)
        if not btc_instrument:
            self.log.error(f"无法找到BTC工具: {self.btc_instrument_id}")
            return None

        symbol_order = self.order_factory.market(
            instrument_id=self.instrument_id,
            order_side=OrderSide.SELL,
            quantity=self.instrument.make_qty(symbol_trade_qty),
        )
        btc_order = self.order_factory.market(
            instrument_id=self.btc_instrument_id,
            order_side=OrderSide.BUY,
            quantity=btc_instrument.make_qty(btc_trade_qty),
        )
        self.submit_order(symbol_order)
        self.submit_order(btc_order)
      
    def _get_technical_indicators(self) -> List[float]:
        """获取技术指标并进行详细分析"""
        print(f"\n📊 ===== 技术指标分析开始 =====")

        # 获取最新K线数据
        current_symbol_bar = self.cache.bar(self.bar_type, index=0)
        if not current_symbol_bar:
            print(f"❌ 无法获取 {self.instrument_id} 的K线数据")
            return None

        current_btc_bar = self.cache.bar(self.btc_bar_type, index=0)
        if not current_btc_bar:
            print(f"❌ 无法获取 {self.btc_instrument_id} 的K线数据")
            return None

        print(f"📈 {self.instrument_id} 当前价格: {current_symbol_bar.close}")
        print(f"📈 {self.btc_instrument_id} 当前价格: {current_btc_bar.close}")

        # 检查指标是否初始化
        symbol_indicators_ready = (self.symbol_atr.initialized and
                                 self.symbol_rsi.initialized and
                                 self.symbol_mfi.initialized)
        btc_indicators_ready = (self.btc_atr.initialized and
                              self.btc_rsi.initialized and
                              self.btc_mfi.initialized)

        if not symbol_indicators_ready:
            print(f"❌ {self.instrument_id} 技术指标未初始化")
            return None
        if not btc_indicators_ready:
            print(f"❌ {self.btc_instrument_id} 技术指标未初始化")
            return None

        # Symbol技术指标
        symbol_natr = self.symbol_atr.value / current_symbol_bar.close
        symbol_mfi = self.symbol_mfi.value
        symbol_rsi = self.symbol_rsi.value

        # BTC技术指标
        btc_natr = self.btc_atr.value / current_btc_bar.close
        btc_mfi = self.btc_mfi.value
        btc_rsi = self.btc_rsi.value

        print(f"📊 {self.instrument_id} 技术指标:")
        print(f"   - NATR: {symbol_natr:.6f}")
        print(f"   - MFI: {symbol_mfi:.2f}")
        print(f"   - RSI: {symbol_rsi:.2f}")

        print(f"📊 {self.btc_instrument_id} 技术指标:")
        print(f"   - NATR: {btc_natr:.6f}")
        print(f"   - MFI: {btc_mfi:.2f}")
        print(f"   - RSI: {btc_rsi:.2f}")

        indicators = [symbol_natr, symbol_mfi, symbol_rsi, btc_natr, btc_mfi, btc_rsi]
        print(f"📊 技术指标向量: {[f'{x:.4f}' for x in indicators]}")
        print(f"📊 ===== 技术指标分析结束 =====\n")

        return indicators

    def on_bar(self, bar: Bar):
        """
        Actions to be performed when the strategy is running and receives a bar.
        Parameters
        ----------
        bar : Bar
            The bar received.
        """
        if bar.bar_type == self.bar_type:
            if self.first_signal:
                executed = self.on_news_signal(self.start_news)
                if executed:
                    self.first_signal = False
            #更新仓位收益率
            self._manage_positions()

    def _manage_positions(self) -> None:
        positions = self.cache.positions_open(strategy_id=self.id)
        current_pnl = 0.0 
        for position in positions:
            if position.instrument_id.value == self.instrument_id.value:
                current_symbol_bar = self.cache.bar(self.bar_type, index=0) 
                if not current_symbol_bar:
                    continue
                current_pnl += self._calculate_position_pnl_rate(position, current_symbol_bar.close)
            elif position.instrument_id.value == self.btc_instrument_id.value:
                current_btc_bar = self.cache.bar(self.btc_bar_type, index=0)  
                if not current_btc_bar:
                    continue
                current_pnl += self._calculate_position_pnl_rate(position, current_btc_bar.close)

        self.log.info(f"Current PnL: {current_pnl}")
        # 更新最高利润记录
        position_key = f"{self.instrument_id.value}_combined"
        if position_key not in self.highest_profit_dict:
            self.highest_profit_dict[position_key] = current_pnl
        else:
            self.highest_profit_dict[position_key] = max(
                self.highest_profit_dict[position_key], current_pnl
            )
        highest_profit = self.highest_profit_dict[position_key]
        # 执行两级别移动止盈 + 一个级别止损
        self._execute_two_tier_trailing_stop(current_pnl, highest_profit)

    def _calculate_position_pnl_rate(self, position, current_price: float) -> float:
        """计算单个仓位的PnL率"""
        total_fee = 2.0 * self.take_fee  # 开仓和平仓费用

        if position.entry == OrderSide.BUY:
            return current_price / float(position.avg_px_open) - 1.0 - total_fee
        else:
            return 1.0 - current_price / float(position.avg_px_open) - total_fee

    def _execute_two_tier_trailing_stop(self,current_pnl_rate: float, highest_profit: float):
        """执行两级别移动止盈 + 一个级别止损"""

        # 1. 固定止损检查
        if current_pnl_rate <= -self.stop_loss_pct:
            self.log.info(f"🔴 触发固定止损: {current_pnl_rate:.3f} <= -{self.stop_loss_pct:.3f}")
            self._close_all_positions("固定止损")
            return

        # Determine current trailing stop tier based on highest profit
        # This approach first computes the tier, then applies the appropriate trailing stop
        trail_tier = 0
        trail_pct = 0.0
        trail_name = ""

        if highest_profit > self.second_trail_profit_threshold:
            # Tier 3: Higher trailing stop (20% retracement) when profit > 3%
            trail_tier = 3
            trail_pct = self.higher_trail_stop_loss_pct
            trail_name = "higher"
        elif highest_profit > self.first_trail_profit_threshold:
            # Tier 2: Standard trailing stop (30% retracement) when profit > 2%
            trail_tier = 2
            trail_pct = self.trail_stop_loss_pct
            trail_name = "standard"
        elif highest_profit > self.low_trail_profit_threshold:
            # Tier 1: Low trailing stop (50% retracement) when profit > 1%
            trail_tier = 1
            trail_pct = self.low_trail_stop_loss_pct
            trail_name = "low"

        # If we have an active trailing stop tier, check if it's triggered
        if trail_tier > 0:
            # Calculate trailing stop threshold based on the tier
            trail_threshold = highest_profit * (1.0 - trail_pct)

            # Check if current profit has fallen below the trailing stop threshold
            if current_pnl_rate < trail_threshold:
                self.log.info(f"🔴 触发{trail_name}级别止盈: {current_pnl_rate:.3f} < {trail_threshold:.3f}")
                self._close_all_positions(f"{trail_name}级别止盈")
                return 

    def _close_all_positions(self, reason: str) -> None:
        """Close all positions with a reason."""
        self.log.info(f"🔴 关闭所有仓位: {reason}")
        for position in self.cache.positions_open(strategy_id=self.id):
            self.close_position(position)

    def on_stop(self):
        self._close_all_positions("停止策略")
